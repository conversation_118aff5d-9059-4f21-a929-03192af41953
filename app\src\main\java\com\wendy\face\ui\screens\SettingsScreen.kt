package com.wendy.face.ui.screens

import android.content.Context
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(onBack: () -> Unit) {
    val context = LocalContext.current
    val sharedPreferences = remember {
        context.getSharedPreferences("face_app_settings", Context.MODE_PRIVATE)
    }

    var personalization by remember { mutableStateOf(sharedPreferences.getString("personalization", "") ?: "") }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("设置") },
                navigationIcon = {
                    IconButton(onClick = onBack) {
                        Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "Back")
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .padding(paddingValues)
                .padding(16.dp)
                .fillMaxSize(),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            OutlinedTextField(
                value = personalization,
                onValueChange = {
                    personalization = it
                    with(sharedPreferences.edit()) {
                        putString("personalization", it)
                        apply()
                    }
                },
                label = { Text("个性化") },
                modifier = Modifier
                    .fillMaxWidth()
                    .fillMaxHeight(0.5f),
                placeholder = { Text("在这里输入您的个性化需求，例如：着重突出如让眼角提升,会对命格产生较好影响。") }
            )
        }
    }
}