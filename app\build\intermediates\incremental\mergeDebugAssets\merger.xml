<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="com.google.mlkit:face-detection:16.1.5" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d87f3ab9ef22f12786eb17a5c9300a5e\transformed\face-detection-16.1.5\assets"><file name="models_bundled/BCLjoy_200.emd" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d87f3ab9ef22f12786eb17a5c9300a5e\transformed\face-detection-16.1.5\assets\models_bundled\BCLjoy_200.emd"/><file name="models_bundled/BCLlefteyeclosed_200.emd" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d87f3ab9ef22f12786eb17a5c9300a5e\transformed\face-detection-16.1.5\assets\models_bundled\BCLlefteyeclosed_200.emd"/><file name="models_bundled/BCLrighteyeclosed_200.emd" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d87f3ab9ef22f12786eb17a5c9300a5e\transformed\face-detection-16.1.5\assets\models_bundled\BCLrighteyeclosed_200.emd"/><file name="models_bundled/blazeface.tfl" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d87f3ab9ef22f12786eb17a5c9300a5e\transformed\face-detection-16.1.5\assets\models_bundled\blazeface.tfl"/><file name="models_bundled/contours.tfl" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d87f3ab9ef22f12786eb17a5c9300a5e\transformed\face-detection-16.1.5\assets\models_bundled\contours.tfl"/><file name="models_bundled/fssd_25_8bit_gray_v2.tflite" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d87f3ab9ef22f12786eb17a5c9300a5e\transformed\face-detection-16.1.5\assets\models_bundled\fssd_25_8bit_gray_v2.tflite"/><file name="models_bundled/fssd_25_8bit_v2.tflite" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d87f3ab9ef22f12786eb17a5c9300a5e\transformed\face-detection-16.1.5\assets\models_bundled\fssd_25_8bit_v2.tflite"/><file name="models_bundled/fssd_anchors_v2.pb" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d87f3ab9ef22f12786eb17a5c9300a5e\transformed\face-detection-16.1.5\assets\models_bundled\fssd_anchors_v2.pb"/><file name="models_bundled/fssd_anchors_v5.pb" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d87f3ab9ef22f12786eb17a5c9300a5e\transformed\face-detection-16.1.5\assets\models_bundled\fssd_anchors_v5.pb"/><file name="models_bundled/fssd_medium_8bit_gray_v5.tflite" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d87f3ab9ef22f12786eb17a5c9300a5e\transformed\face-detection-16.1.5\assets\models_bundled\fssd_medium_8bit_gray_v5.tflite"/><file name="models_bundled/fssd_medium_8bit_v5.tflite" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d87f3ab9ef22f12786eb17a5c9300a5e\transformed\face-detection-16.1.5\assets\models_bundled\fssd_medium_8bit_v5.tflite"/><file name="models_bundled/LMprec_600.emd" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d87f3ab9ef22f12786eb17a5c9300a5e\transformed\face-detection-16.1.5\assets\models_bundled\LMprec_600.emd"/><file name="models_bundled/MFT_fssd_accgray.pb" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d87f3ab9ef22f12786eb17a5c9300a5e\transformed\face-detection-16.1.5\assets\models_bundled\MFT_fssd_accgray.pb"/><file name="models_bundled/MFT_fssd_fastgray.pb" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d87f3ab9ef22f12786eb17a5c9300a5e\transformed\face-detection-16.1.5\assets\models_bundled\MFT_fssd_fastgray.pb"/></source></dataSet><dataSet config="com.google.mlkit:face-mesh-detection:16.0.0-beta1" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476b0b08ad0be6fbe1ccabec4c32b172\transformed\face-mesh-detection-16.0.0-beta1\assets"><file name="mlkit_facemesh/data/geometry_pipeline_metadata_landmarks.binarypb" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476b0b08ad0be6fbe1ccabec4c32b172\transformed\face-mesh-detection-16.0.0-beta1\assets\mlkit_facemesh\data\geometry_pipeline_metadata_landmarks.binarypb"/><file name="mlkit_facemesh/facedetector-front.f16.tflite" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476b0b08ad0be6fbe1ccabec4c32b172\transformed\face-mesh-detection-16.0.0-beta1\assets\mlkit_facemesh\facedetector-front.f16.tflite"/><file name="mlkit_facemesh/face_landmark_with_attention.tflite" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476b0b08ad0be6fbe1ccabec4c32b172\transformed\face-mesh-detection-16.0.0-beta1\assets\mlkit_facemesh\face_landmark_with_attention.tflite"/><file name="mlkit_facemesh/face_mesh_graph.binarypb" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476b0b08ad0be6fbe1ccabec4c32b172\transformed\face-mesh-detection-16.0.0-beta1\assets\mlkit_facemesh\face_mesh_graph.binarypb"/><file name="mlkit_facemesh/face_short_range_graph.binarypb" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476b0b08ad0be6fbe1ccabec4c32b172\transformed\face-mesh-detection-16.0.0-beta1\assets\mlkit_facemesh\face_short_range_graph.binarypb"/></source></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\workspace\gitee.com\wendy\face\app\src\main\assets"><file name="icon.png" path="D:\workspace\gitee.com\wendy\face\app\src\main\assets\icon.png"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\workspace\gitee.com\wendy\face\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\workspace\gitee.com\wendy\face\app\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>