<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="Theme.Face" parent="android:Theme.Material.Light.NoActionBar" />

    <!-- 全面屏主题，隐藏状态栏和导航栏 -->
    <style name="Theme.Face.FullScreen" parent="Theme.Face">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="android:fitsSystemWindows">false</item>
    </style>
</resources>